# 西科大高层次人才管理系统启动脚本
param()

Write-Host "启动西科大高层次人才管理系统..." -ForegroundColor Green

# 检查Docker是否安装
function Test-DockerInstalled {
    try {
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker已安装: $dockerVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Docker未安装或未启动" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Docker未安装或未启动" -ForegroundColor Red
        return $false
    }
}

# 检查Docker Compose是否可用
function Test-DockerComposeInstalled {
    try {
        $composeVersion = & docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker Compose已安装: $composeVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Docker Compose未安装" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Docker Compose未安装" -ForegroundColor Red
        return $false
    }
}

# 检查Docker服务是否运行
function Test-DockerRunning {
    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker服务正在运行" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Docker服务未运行" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Docker服务未运行" -ForegroundColor Red
        return $false
    }
}

# 环境检查
Write-Host ""
Write-Host "正在检查环境..." -ForegroundColor Yellow

$dockerInstalled = Test-DockerInstalled
$composeInstalled = Test-DockerComposeInstalled
$dockerRunning = Test-DockerRunning

if (-not $dockerInstalled) {
    Write-Host ""
    Write-Host "请先安装Docker Desktop:" -ForegroundColor Red
    Write-Host "下载地址: https://www.docker.com/products/docker-desktop/" -ForegroundColor Cyan
    Write-Host "安装完成后请重启计算机并启动Docker Desktop" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

if (-not $composeInstalled) {
    Write-Host ""
    Write-Host "请确保Docker Compose已正确安装" -ForegroundColor Red
    Write-Host "通常Docker Desktop会自动包含Docker Compose" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

if (-not $dockerRunning) {
    Write-Host ""
    Write-Host "请启动Docker Desktop服务" -ForegroundColor Red
    Write-Host "1. 打开Docker Desktop应用程序" -ForegroundColor Yellow
    Write-Host "2. 等待Docker引擎启动完成" -ForegroundColor Yellow
    Write-Host "3. 重新运行此脚本" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

# 启动服务
Write-Host ""
Write-Host "环境检查通过，开始启动服务..." -ForegroundColor Green

try {
    Write-Host "正在构建并启动所有服务..." -ForegroundColor Yellow
    & docker-compose up --build -d

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✓ 服务启动完成！" -ForegroundColor Green
        Write-Host "前端访问地址: http://localhost" -ForegroundColor Cyan
        Write-Host "后台API地址: http://localhost:3000" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "常用命令:" -ForegroundColor Yellow
        Write-Host "查看服务状态: docker-compose ps" -ForegroundColor White
        Write-Host "查看日志: docker-compose logs -f" -ForegroundColor White
        Write-Host "停止服务: .\stop.ps1 或 docker-compose down" -ForegroundColor White
    }
    else {
        Write-Host ""
        Write-Host "✗ 服务启动失败，请检查错误信息" -ForegroundColor Red
    }
}
catch {
    Write-Host ""
    Write-Host "✗ 启动过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
Read-Host

