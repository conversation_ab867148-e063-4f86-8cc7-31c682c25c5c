# ????????????????????????
param()

# ???????????$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "?????????????????????..." -ForegroundColor Green

# ????ocker??????
function Test-DockerInstalled {
    try {
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "??Docker????? $dockerVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "??Docker??????????? -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "??Docker??????????? -ForegroundColor Red
        return $false
    }
}

# ????ocker Compose??????
function Test-DockerComposeInstalled {
    try {
        $composeVersion = & docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "??Docker Compose????? $composeVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "??Docker Compose????? -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "??Docker Compose????? -ForegroundColor Red
        return $false
    }
}

# ????ocker?????????
function Test-DockerRunning {
    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "??Docker?????????" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "??Docker???????? -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "??Docker???????? -ForegroundColor Red
        return $false
    }
}

# ???????Write-Host ""
Write-Host "??????????.." -ForegroundColor Yellow

$dockerInstalled = Test-DockerInstalled
$composeInstalled = Test-DockerComposeInstalled
$dockerRunning = Test-DockerRunning

if (-not $dockerInstalled) {
    Write-Host ""
    Write-Host "??????Docker Desktop:" -ForegroundColor Red
    Write-Host "??????: https://www.docker.com/products/docker-desktop/" -ForegroundColor Cyan
    Write-Host "?????????????????????Docker Desktop" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "??????????
    exit 1
}

if (-not $composeInstalled) {
    Write-Host ""
    Write-Host "?????ocker Compose???????? -ForegroundColor Red
    Write-Host "???Docker Desktop????????ocker Compose" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "??????????
    exit 1
}

if (-not $dockerRunning) {
    Write-Host ""
    Write-Host "?????ocker Desktop???" -ForegroundColor Red
    Write-Host "1. ???Docker Desktop??????" -ForegroundColor Yellow
    Write-Host "2. ???Docker?????????" -ForegroundColor Yellow
    Write-Host "3. ??????????? -ForegroundColor Yellow
    Write-Host ""
    Read-Host "??????????
    exit 1
}

# ??????
Write-Host ""
Write-Host "?????????????????????.." -ForegroundColor Green

try {
    Write-Host "?????????????????.." -ForegroundColor Yellow
    & docker-compose up --build -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "????????????? -ForegroundColor Green
        Write-Host "?????????: http://localhost" -ForegroundColor Cyan
        Write-Host "???API???: http://localhost:3000" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "??????:" -ForegroundColor Yellow
        Write-Host "?????????? docker-compose ps" -ForegroundColor White
        Write-Host "??????: docker-compose logs -f" -ForegroundColor White
        Write-Host "??????: .\stop.ps1 ??docker-compose down" -ForegroundColor White
    }
    else {
        Write-Host ""
        Write-Host "???????????????????????? -ForegroundColor Red
    }
}
catch {
    Write-Host ""
    Write-Host "???????????????? $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "??????????.." -ForegroundColor Gray
Read-Host


