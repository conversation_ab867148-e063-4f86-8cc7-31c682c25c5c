@echo off
chcp 65001 >nul
echo 启动西科大高层次人才管理系统...

echo.
echo 正在检查环境...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Docker未安装或未启动
    echo.
    echo 请先安装Docker Desktop:
    echo 下载地址: https://www.docker.com/products/docker-desktop/
    echo 安装完成后请重启计算机并启动Docker Desktop
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Docker已安装
)

REM 检查Docker Compose是否可用
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Docker Compose未安装
    echo.
    echo 请确保Docker Compose已正确安装
    echo 通常Docker Desktop会自动包含Docker Compose
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Docker Compose已安装
)

REM 检查Docker服务是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Docker服务未运行
    echo.
    echo 请启动Docker Desktop服务
    echo 1. 打开Docker Desktop应用程序
    echo 2. 等待Docker引擎启动完成
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Docker服务正在运行
)

REM 启动服务
echo.
echo 环境检查通过，开始启动服务...
echo 正在构建并启动所有服务...

docker-compose up --build -d
if %errorlevel% equ 0 (
    echo.
    echo ✓ 服务启动完成！
    echo 前端访问地址: http://localhost
    echo 后台API地址: http://localhost:3000
    echo.
    echo 常用命令:
    echo 查看服务状态: docker-compose ps
    echo 查看日志: docker-compose logs -f
    echo 停止服务: stop.bat 或 docker-compose down
) else (
    echo.
    echo ✗ 服务启动失败，请检查错误信息
)

echo.
pause