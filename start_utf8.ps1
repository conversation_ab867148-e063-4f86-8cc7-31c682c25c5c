﻿# 瑗跨澶ч珮灞傛浜烘墠绠＄悊绯荤粺鍚姩鑴氭湰
param()

# 璁剧疆鎺у埗鍙扮紪鐮?$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "鍚姩瑗跨澶ч珮灞傛浜烘墠绠＄悊绯荤粺..." -ForegroundColor Green

# 妫€鏌ocker鏄惁瀹夎
function Test-DockerInstalled {
    try {
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "鉁?Docker宸插畨瑁? $dockerVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "鉁?Docker鏈畨瑁呮垨鏈惎鍔? -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "鉁?Docker鏈畨瑁呮垨鏈惎鍔? -ForegroundColor Red
        return $false
    }
}

# 妫€鏌ocker Compose鏄惁鍙敤
function Test-DockerComposeInstalled {
    try {
        $composeVersion = & docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "鉁?Docker Compose宸插畨瑁? $composeVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "鉁?Docker Compose鏈畨瑁? -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "鉁?Docker Compose鏈畨瑁? -ForegroundColor Red
        return $false
    }
}

# 妫€鏌ocker鏈嶅姟鏄惁杩愯
function Test-DockerRunning {
    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "鉁?Docker鏈嶅姟姝ｅ湪杩愯" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "鉁?Docker鏈嶅姟鏈繍琛? -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "鉁?Docker鏈嶅姟鏈繍琛? -ForegroundColor Red
        return $false
    }
}

# 鐜妫€鏌?Write-Host ""
Write-Host "姝ｅ湪妫€鏌ョ幆澧?.." -ForegroundColor Yellow

$dockerInstalled = Test-DockerInstalled
$composeInstalled = Test-DockerComposeInstalled
$dockerRunning = Test-DockerRunning

if (-not $dockerInstalled) {
    Write-Host ""
    Write-Host "璇峰厛瀹夎Docker Desktop:" -ForegroundColor Red
    Write-Host "涓嬭浇鍦板潃: https://www.docker.com/products/docker-desktop/" -ForegroundColor Cyan
    Write-Host "瀹夎瀹屾垚鍚庤閲嶅惎璁＄畻鏈哄苟鍚姩Docker Desktop" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "鎸変换鎰忛敭閫€鍑?
    exit 1
}

if (-not $composeInstalled) {
    Write-Host ""
    Write-Host "璇风‘淇滵ocker Compose宸叉纭畨瑁? -ForegroundColor Red
    Write-Host "閫氬父Docker Desktop浼氳嚜鍔ㄥ寘鍚獶ocker Compose" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "鎸変换鎰忛敭閫€鍑?
    exit 1
}

if (-not $dockerRunning) {
    Write-Host ""
    Write-Host "璇峰惎鍔―ocker Desktop鏈嶅姟" -ForegroundColor Red
    Write-Host "1. 鎵撳紑Docker Desktop搴旂敤绋嬪簭" -ForegroundColor Yellow
    Write-Host "2. 绛夊緟Docker寮曟搸鍚姩瀹屾垚" -ForegroundColor Yellow
    Write-Host "3. 閲嶆柊杩愯姝よ剼鏈? -ForegroundColor Yellow
    Write-Host ""
    Read-Host "鎸変换鎰忛敭閫€鍑?
    exit 1
}

# 鍚姩鏈嶅姟
Write-Host ""
Write-Host "鐜妫€鏌ラ€氳繃锛屽紑濮嬪惎鍔ㄦ湇鍔?.." -ForegroundColor Green

try {
    Write-Host "姝ｅ湪鏋勫缓骞跺惎鍔ㄦ墍鏈夋湇鍔?.." -ForegroundColor Yellow
    & docker-compose up --build -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "鉁?鏈嶅姟鍚姩瀹屾垚锛? -ForegroundColor Green
        Write-Host "鍓嶇璁块棶鍦板潃: http://localhost" -ForegroundColor Cyan
        Write-Host "鍚庡彴API鍦板潃: http://localhost:3000" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "甯哥敤鍛戒护:" -ForegroundColor Yellow
        Write-Host "鏌ョ湅鏈嶅姟鐘舵€? docker-compose ps" -ForegroundColor White
        Write-Host "鏌ョ湅鏃ュ織: docker-compose logs -f" -ForegroundColor White
        Write-Host "鍋滄鏈嶅姟: .\stop.ps1 鎴?docker-compose down" -ForegroundColor White
    }
    else {
        Write-Host ""
        Write-Host "鉁?鏈嶅姟鍚姩澶辫触锛岃妫€鏌ラ敊璇俊鎭? -ForegroundColor Red
    }
}
catch {
    Write-Host ""
    Write-Host "鉁?鍚姩杩囩▼涓彂鐢熼敊璇? $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "鎸変换鎰忛敭閫€鍑?.." -ForegroundColor Gray
Read-Host


