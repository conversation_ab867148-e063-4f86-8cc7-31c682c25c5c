# XKD High-level Talent Management System Startup Script
param()

# Set console encoding
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Starting XKD High-level Talent Management System..." -ForegroundColor Green

# Check if Docker is installed
function Test-DockerInstalled {
    try {
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker installed: $dockerVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Docker not installed or not running" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Docker not installed or not running" -ForegroundColor Red
        return $false
    }
}

# Check if Docker Compose is available
function Test-DockerComposeInstalled {
    try {
        $composeVersion = & docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker Compose installed: $composeVersion" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Docker Compose not installed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Docker Compose not installed" -ForegroundColor Red
        return $false
    }
}

# Check if Docker service is running
function Test-DockerRunning {
    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker service is running" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Docker service not running" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Docker service not running" -ForegroundColor Red
        return $false
    }
}

# Environment check
Write-Host ""
Write-Host "Checking environment..." -ForegroundColor Yellow

$dockerInstalled = Test-DockerInstalled
$composeInstalled = Test-DockerComposeInstalled
$dockerRunning = Test-DockerRunning

if (-not $dockerInstalled) {
    Write-Host ""
    Write-Host "Please install Docker Desktop first:" -ForegroundColor Red
    Write-Host "Download: https://www.docker.com/products/docker-desktop/" -ForegroundColor Cyan
    Write-Host "After installation, restart your computer and start Docker Desktop" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press any key to exit"
    exit 1
}

if (-not $composeInstalled) {
    Write-Host ""
    Write-Host "Please ensure Docker Compose is properly installed" -ForegroundColor Red
    Write-Host "Usually Docker Desktop includes Docker Compose automatically" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press any key to exit"
    exit 1
}

if (-not $dockerRunning) {
    Write-Host ""
    Write-Host "Please start Docker Desktop service" -ForegroundColor Red
    Write-Host "1. Open Docker Desktop application" -ForegroundColor Yellow
    Write-Host "2. Wait for Docker engine to start" -ForegroundColor Yellow
    Write-Host "3. Run this script again" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press any key to exit"
    exit 1
}

# Start services
Write-Host ""
Write-Host "Environment check passed, starting services..." -ForegroundColor Green

try {
    Write-Host "Building and starting all services..." -ForegroundColor Yellow
    & docker-compose up --build -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✓ Services started successfully!" -ForegroundColor Green
        Write-Host "Frontend URL: http://localhost" -ForegroundColor Cyan
        Write-Host "Backend API URL: http://localhost:3000" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Common commands:" -ForegroundColor Yellow
        Write-Host "Check service status: docker-compose ps" -ForegroundColor White
        Write-Host "View logs: docker-compose logs -f" -ForegroundColor White
        Write-Host "Stop services: .\stop.ps1 or docker-compose down" -ForegroundColor White
    }
    else {
        Write-Host ""
        Write-Host "✗ Service startup failed, please check error messages" -ForegroundColor Red
    }
}
catch {
    Write-Host ""
    Write-Host "✗ Error occurred during startup: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
Read-Host
