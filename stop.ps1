# 西科大高层次人才管理系统停止脚本
param()

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "停止西科大高层次人才管理系统..." -ForegroundColor Yellow

# 检查Docker是否可用
try {
    & docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Docker服务未运行，无法停止容器" -ForegroundColor Red
        Write-Host ""
        Read-Host "按任意键退出"
        exit 1
    }
}
catch {
    Write-Host "✗ Docker不可用" -ForegroundColor Red
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

# 停止服务
try {
    Write-Host "正在停止所有服务..." -ForegroundColor Yellow
    & docker-compose down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✓ 服务已停止！" -ForegroundColor Green
        Write-Host ""
        Write-Host "其他选项:" -ForegroundColor Yellow
        Write-Host "清理所有数据: docker-compose down --volumes" -ForegroundColor White
        Write-Host "清理镜像: docker-compose down --rmi all" -ForegroundColor White
        Write-Host "完全清理: docker-compose down --volumes --rmi all" -ForegroundColor White
    }
    else {
        Write-Host ""
        Write-Host "✗ 停止服务时发生错误" -ForegroundColor Red
    }
}
catch {
    Write-Host ""
    Write-Host "✗ 停止过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
Read-Host
