# 高层次人才服务管理平台 (gcc-xkd)

## 项目简介

西安科技大学高层次人才服务管理平台，用于管理和服务高层次人才的综合性Web应用系统。

## 项目结构

```
gcc-xkd/
├── apiServer/          # 后台API服务 (Node.js + Restify + MongoDB)
├── myClient/           # 前端Web应用 (React + Ant Design Pro)
├── docker-compose.yml  # Docker容器编排配置
└── start.sh           # 一键启动脚本
```

## 技术栈

### 后台服务 (apiServer)
- **Node.js** - 运行环境
- **Restify** - REST API框架  
- **MongoDB** - 数据库
- **PM2** - 进程管理

### 前端应用 (myClient)
- **React 16.7.0** - 前端框架
- **Ant Design Pro** - UI组件库
- **Dva** - 状态管理
- **UmiJS** - 构建工具

## 快速开始

### 使用Docker部署（推荐）

1. **确保已安装Docker和Docker Compose**

2. **一键启动所有服务**
   
   **Windows (PowerShell - 推荐):**
   ```powershell
   .\start.ps1
   ```
   
   **Windows (命令提示符):**
   ```cmd
   start.bat
   ```
   
   **Linux/macOS:**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```
   
   **通用方式:**
   ```bash
   docker-compose up --build -d
   ```

3. **停止服务**
   
   **Windows (PowerShell):**
   ```powershell
   .\stop.ps1
   ```
   
   **Windows (命令提示符):**
   ```cmd
   stop.bat
   ```
   
   **Linux/macOS:**
   ```bash
   ./stop.sh
   ```

4. **访问应用**
   - 前端应用：http://localhost
   - 后台API：http://localhost:3000
   - MongoDB：localhost:27017

5. **常用Docker命令**
   ```bash
   # 查看服务状态
   docker-compose ps
   
   # 查看日志
   docker-compose logs -f
   
   # 重新构建并启动
   docker-compose up --build -d
   
   # 清理所有容器和数据
   docker-compose down --volumes --rmi all
   ```

### 本地开发环境

#### 后台服务启动
```bash
cd apiServer
npm install
npm start
```

#### 前端应用启动
```bash
cd myClient
npm install --legacy-peer-deps
npm start
```

## 主要功能

- **人才信息管理** - 高层次人才基本信息维护
- **目标管理** - 待遇协议、论文目标、项目目标设定
- **成果管理** - 待遇兑现、论文成果、项目成果记录
- **数据统计** - 多维度数据分析和可视化
- **汇总导出** - Excel格式报表导出
- **多语言支持** - 中文简体、中文繁体、英文

## 环境要求

### Docker部署
- **Docker**: >= 20.x
- **Docker Compose**: >= 1.29.x
- **操作系统**: Windows 10/11, macOS, Linux

### Windows用户说明
- **推荐**: 使用PowerShell脚本（`start.ps1`、`stop.ps1`）
- **备选**: 使用批处理文件（`start.bat`、`stop.bat`）
- **注意**: 脚本会自动检查Docker环境并给出安装提示
- **权限**: 如果PowerShell执行策略限制，请以管理员身份运行：
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```

#### PowerShell执行策略问题解决

如果运行PowerShell脚本时遇到执行策略错误，请使用以下方法之一：

**方法1：设置执行策略（推荐）**
1. 以管理员身份打开PowerShell
2. 运行以下命令：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
3. 输入 `Y` 确认
4. 然后正常运行脚本：
   ```powershell
   .\start.ps1
   ```

**方法2：绕过执行策略**
```powershell
PowerShell -ExecutionPolicy Bypass -File .\start.ps1
```

**方法3：使用批处理文件（最简单）**
如果PowerShell问题较多，直接使用批处理文件：
```cmd
start.bat
```

#### 常见问题排查
- **脚本无法运行**: 检查PowerShell执行策略
- **Docker相关错误**: 确保Docker Desktop已安装并正在运行
- **端口占用**: 检查80端口和3000端口是否被占用
- **权限问题**: 以管理员身份运行命令提示符或PowerShell

### 环境检查功能
启动脚本会自动检查：
- ✓ Docker是否已安装
- ✓ Docker Compose是否可用  
- ✓ Docker服务是否正在运行
- ✗ 如有问题会给出具体的安装或启动指导

### Linux/macOS用户说明
- 使用`start.sh`和`stop.sh`脚本
- 首次使用需要添加执行权限：`chmod +x *.sh`

## 开发说明

### 前端开发注意事项
- 使用 `npm install --legacy-peer-deps` 解决依赖版本冲突
- 支持多环境配置：local、dev、production
- 内置Mock数据支持

### 后台开发注意事项  
- 自动执行数据库初始化
- 支持CORS跨域配置
- 文件上传功能支持

## 部署架构

Docker部署包含以下服务：
- **MongoDB容器** - 数据存储
- **API服务容器** - 后台服务（PM2管理）
- **Web服务容器** - 前端应用（Nginx部署）

## 许可证

ISC License

## 作者

西安科技大学




