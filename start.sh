#!/bin/bash

echo "启动西科大高层次人才管理系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查Docker是否安装
check_docker() {
    if command -v docker &> /dev/null; then
        echo -e "${GREEN}✓ Docker已安装: $(docker --version)${NC}"
        return 0
    else
        echo -e "${RED}✗ Docker未安装${NC}"
        return 1
    fi
}

# 检查Docker Compose是否安装
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        echo -e "${GREEN}✓ Docker Compose已安装: $(docker-compose --version)${NC}"
        return 0
    else
        echo -e "${RED}✗ Docker Compose未安装${NC}"
        return 1
    fi
}

# 检查Docker服务是否运行
check_docker_running() {
    if docker info &> /dev/null; then
        echo -e "${GREEN}✓ Docker服务正在运行${NC}"
        return 0
    else
        echo -e "${RED}✗ Docker服务未运行${NC}"
        return 1
    fi
}

# 环境检查
echo -e "\n${YELLOW}正在检查环境...${NC}"

if ! check_docker; then
    echo -e "\n${RED}请先安装Docker:${NC}"
    echo -e "${CYAN}Ubuntu/Debian: sudo apt-get install docker.io${NC}"
    echo -e "${CYAN}CentOS/RHEL: sudo yum install docker${NC}"
    echo -e "${CYAN}macOS: brew install docker 或下载Docker Desktop${NC}"
    exit 1
fi

if ! check_docker_compose; then
    echo -e "\n${RED}请先安装Docker Compose:${NC}"
    echo -e "${CYAN}sudo curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose${NC}"
    echo -e "${CYAN}sudo chmod +x /usr/local/bin/docker-compose${NC}"
    exit 1
fi

if ! check_docker_running; then
    echo -e "\n${RED}请启动Docker服务:${NC}"
    echo -e "${CYAN}sudo systemctl start docker${NC}"
    echo -e "${CYAN}或者启动Docker Desktop应用程序${NC}"
    exit 1
fi

# 启动服务
echo -e "\n${GREEN}环境检查通过，开始启动服务...${NC}"

if docker-compose up --build -d; then
    echo ""
    echo -e "${GREEN}✓ 服务启动完成！${NC}"
    echo -e "${CYAN}前端访问地址: http://localhost${NC}"
    echo -e "${CYAN}后台API地址: http://localhost:3000${NC}"
    echo ""
    echo -e "${YELLOW}常用命令:${NC}"
    echo "查看服务状态: docker-compose ps"
    echo "查看日志: docker-compose logs -f"
    echo "停止服务: ./stop.sh 或 docker-compose down"
else
    echo -e "\n${RED}✗ 服务启动失败，请检查错误信息${NC}"
    exit 1
fi


